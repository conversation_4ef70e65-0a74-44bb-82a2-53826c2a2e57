#include "PcdPreprocess.hpp"

#include <chrono>
#include <spdlog/spdlog.h>
#include <gtest/gtest.h>
#include <pcl/io/ply_io.h>


/*
 * 以下全部单元测试约定点云的座标都为偶数，限制值都为奇数
 */
class PcdPreprocessTest : public ::testing::Test
{
protected:

    pcl::PointCloud<pcl::PointXYZ>::Ptr minmax_cube = pcl::PointCloud<pcl::PointXYZ>().makeShared();
    pcl::PointCloud<pcl::PointXYZ>::Ptr box_cube = pcl::PointCloud<pcl::PointXYZ>().makeShared();

    const int POINT_DISTANCE = 2;
    const int MINMAX_CUBE_EDGE_POINT_COUNT = 7;
    const int BOX_CUBE_EDGE_POINT_COUNT = 10;


    void SetUp() override
    {
        spdlog::set_level(spdlog::level::debug);

        for (int x = 0; x < MINMAX_CUBE_EDGE_POINT_COUNT; ++x) {
            for (int y = 0; y < MINMAX_CUBE_EDGE_POINT_COUNT; ++y) {
                for (int z = 0; z < MINMAX_CUBE_EDGE_POINT_COUNT; ++z) {
                    minmax_cube->push_back(pcl::PointXYZ(x*POINT_DISTANCE, y*POINT_DISTANCE, z*POINT_DISTANCE));
                }
            }
        }

        for (int x = 0; x < BOX_CUBE_EDGE_POINT_COUNT; ++x) {
            for (int y = 0; y < BOX_CUBE_EDGE_POINT_COUNT; ++y) {
                for (int z = 0; z < BOX_CUBE_EDGE_POINT_COUNT; ++z) {
                    box_cube->push_back(pcl::PointXYZ(x*POINT_DISTANCE, y*POINT_DISTANCE, z*POINT_DISTANCE));
                }
            }
        }

    }
};

TEST_F(PcdPreprocessTest, X最小限制值前无点)
{
    /*
    源点云：             [X边界:0]---------------[X边界:12]
    参数：    [X min:-1]----------------------------------------------[正无穷]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -1;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最小限制值前有点)
{
    /*
    源点云：             [X边界:0]---------------[X边界:12]
    参数：                            [X min:11]--------------------[正无穷]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 11;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 1);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最大限制值后无点)
{
    /*
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------------------------------[X max:13]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.max_x = 13;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最大限制值后有点)
{
    /*
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------[X max:3]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.max_x = 3;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 2);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云在X限制区间后)
{
    /*
    源点云：                               [X边界:0]---------------[X边界:12]
    参数：    [X min:-3]--------[X max:-1]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -3;
    cfg.max_x = -1;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间与源点云有交集1)
{
    /*
    源点云：              [X边界:0]---------------[X边界:12]
    参数：    [X min:-1]------------[X max:5]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -1;
    cfg.max_x = 5;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 3);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间与源点云有交集2)
{
    /*
    源点云：         [X边界:0]---------------[X边界:12]
    参数：                     [X min:5]--------------[X max:14]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 5;
    cfg.max_x = 13;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 4);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间在源点云X区间内)
{
    /*
    源点云：         [X边界:0]----------------------------[X边界:12]
    参数：                     [X min:1]-----[X max:11]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 1;
    cfg.max_x = 11;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 5);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云在X限制区间前)
{
    /*
    源点云：      [X边界:0]---------------[X边界:12]
    参数：                                           [X min:13]--------[X max:15]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 13;
    cfg.max_x = 15;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 没有任何限制)
{
    algorithm::PcdCropMinmaxXYZ alg;
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 同时限制多个维度)
{
    /*同时限制X、Y*/
    algorithm::PcdCropMinmaxXYZ alg;
    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*2*MINMAX_CUBE_EDGE_POINT_COUNT);

    /*同时限制X、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*MINMAX_CUBE_EDGE_POINT_COUNT*3);

    /*同时限制Y、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*2*3);

    /*同时限制X、Y、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*2*3);

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒外)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(100, 100, 100);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;


    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云部分在包围盒内)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(19, 19, 19);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 2*2*2);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒内且包围盒无旋转)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(9, 9, 9);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 4*4*4);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒内且包围盒带旋转)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(9, 9, 9);
    box.quat.x() = 0.3826834;/*绕X轴旋转45度*/
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 0.9238795;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 48);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 多个包围盒)
{
    algorithm::BoxInfo box1,box2;
    box1.lengthx = box1.widthy = box1.depthz = box2.lengthx = box2.widthy = box2.depthz = 8;
    box1.translation = Eigen::Vector3f(9, 9, 9);
    box2.translation = Eigen::Vector3f(13, 13, 13);
    box1.quat.x() = box2.quat.x() = 0;
    box1.quat.y() = box2.quat.y() = 0;
    box1.quat.z() = box2.quat.z() = 0;
    box1.quat.w() = box2.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box1,box2});
    auto results = alg.computeOutputList(box_cube);
    ASSERT_EQ(results.size(), 2);
    SPDLOG_DEBUG("cropbox result 1 point size: {},result 2 point size:{}",results[0]->size(),results[1]->size());
    ASSERT_EQ(results[0]->size(), 4*4*4);
    ASSERT_EQ(results[1]->size(), 4*4*4);

    auto mergeResult = alg.compute(box_cube);
    SPDLOG_DEBUG("multi cropbox merge result point size: {}",mergeResult->size());
    ASSERT_EQ(mergeResult->size(), 4*4*4 + 4*4*4);

    SUCCEED();
}

// ==================== 反向ROI测试用例 ====================

TEST_F(PcdPreprocessTest, XYZ反向ROI测试)
{
    /*
    测试反向ROI功能：保留X坐标范围外的点
    源点云：             [X边界:0]---------------[X边界:12]
    参数：                     [X min:1]-----[X max:11]  (反向)
    预期结果：保留X=0和X=12的点
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 1;
    cfg.max_x = 11;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);

    // 正向ROI测试
    auto normal_result = alg.compute(minmax_cube, false);
    SPDLOG_DEBUG("正向ROI结果点数: {}", normal_result->size());
    ASSERT_EQ(normal_result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 5);

    // 反向ROI测试
    auto inverse_result = alg.compute(minmax_cube, true);
    SPDLOG_DEBUG("反向ROI结果点数: {}", inverse_result->size());
    ASSERT_EQ(inverse_result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 2);

    // 验证正向+反向 = 原始点云
    ASSERT_EQ(normal_result->size() + inverse_result->size(), minmax_cube->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 包围盒反向ROI测试)
{
    /*
    测试包围盒反向ROI：保留包围盒外的点
    */
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(9, 9, 9);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});

    // 正向ROI测试
    auto normal_result = alg.compute(box_cube, false);
    SPDLOG_DEBUG("包围盒正向ROI结果点数: {}", normal_result->size());
    ASSERT_EQ(normal_result->size(), 4*4*4);

    // 反向ROI测试
    auto inverse_result = alg.compute(box_cube, true);
    SPDLOG_DEBUG("包围盒反向ROI结果点数: {}", inverse_result->size());
    ASSERT_EQ(inverse_result->size(), box_cube->size() - 4*4*4);

    // 验证正向+反向 = 原始点云
    ASSERT_EQ(normal_result->size() + inverse_result->size(), box_cube->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 多包围盒正向和反向ROI逻辑测试)
{
    /*
    测试多个包围盒的正向和反向ROI逻辑：
    - 正向ROI：OR关系，在任意包围盒内的点（需要去重）
    - 反向ROI：AND关系，不在任何包围盒内的点
    */
    algorithm::BoxInfo box1, box2;
    box1.lengthx = box1.widthy = box1.depthz = box2.lengthx = box2.widthy = box2.depthz = 8;
    box1.translation = Eigen::Vector3f(9, 9, 9);
    box2.translation = Eigen::Vector3f(13, 13, 13);
    box1.quat.x() = box2.quat.x() = 0;
    box1.quat.y() = box2.quat.y() = 0;
    box1.quat.z() = box2.quat.z() = 0;
    box1.quat.w() = box2.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box1, box2});

    // 正向ROI测试：并集关系，在任意包围盒内的点
    auto normal_merge = alg.compute(box_cube, false);
    SPDLOG_DEBUG("多包围盒正向ROI结果点数: {}", normal_merge->size());

    // 验证正向ROI的去重逻辑
    // 获取单个包围盒的结果进行对比
    algorithm::PcdCropBox single_alg1, single_alg2;
    single_alg1.init_config({box1});
    single_alg2.init_config({box2});
    auto box1_result = single_alg1.compute(box_cube, false);
    auto box2_result = single_alg2.compute(box_cube, false);

    SPDLOG_DEBUG("包围盒1单独结果: {}, 包围盒2单独结果: {}", box1_result->size(), box2_result->size());

    // 正向ROI结果应该 <= 两个包围盒结果之和（因为去重）
    ASSERT_LE(normal_merge->size(), box1_result->size() + box2_result->size());
    // 正向ROI结果应该 >= 任意单个包围盒结果（因为是并集）
    ASSERT_GE(normal_merge->size(), box1_result->size());
    ASSERT_GE(normal_merge->size(), box2_result->size());

    // 反向ROI测试：交集关系，不在任何包围盒内的点
    auto inverse_merge = alg.compute(box_cube, true);
    SPDLOG_DEBUG("多包围盒反向ROI结果点数: {}", inverse_merge->size());

    // 验证反向ROI的正确性
    ASSERT_GT(inverse_merge->size(), 0);  // 应该有剩余的点
    ASSERT_LT(inverse_merge->size(), box_cube->size());  // 应该少于原始点云

    // 关键验证：正向ROI + 反向ROI = 原始点云（无重叠）
    ASSERT_EQ(normal_merge->size() + inverse_merge->size(), box_cube->size());

    // 验证反向ROI的逐步过滤逻辑
    auto after_box1 = single_alg1.compute(box_cube, true);  // 不在box1内的点
    auto final_result = single_alg2.compute(after_box1, true);  // 进一步不在box2内的点
    ASSERT_EQ(inverse_merge->size(), final_result->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 凸包正向ROI测试)
{
    /*参考上面box的测试case，构造一个8*8*8的小正方体凸包，其中心点与点云正方体的中心点重合 */
    std::vector<pcl::PointXYZ> hullPoints{
        {5, 5, 5},
        {13, 5, 5},
        {13, 13, 5},
        {5, 13, 5},
        {5, 5, 13},
        {13, 5, 13},
        {13, 13, 13},
        {5, 13, 13},
    };

    algorithm::PcdCropHull alg;
    alg.init_config(hullPoints);
    auto result = alg.compute(box_cube, false);
    SPDLOG_DEBUG("凸包正向ROI结果点数: {}", result->size());
    ASSERT_EQ(result->size(), 4*4*4);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 凸包反向ROI测试)
{
    /*测试凸包反向ROI：保留凸包外的点*/
    std::vector<pcl::PointXYZ> hullPoints{
        {5, 5, 5},
        {13, 5, 5},
        {13, 13, 5},
        {5, 13, 5},
        {5, 5, 13},
        {13, 5, 13},
        {13, 13, 13},
        {5, 13, 13},
    };

    algorithm::PcdCropHull alg;
    alg.init_config(hullPoints);

    // 正向ROI测试
    auto normal_result = alg.compute(box_cube, false);
    SPDLOG_DEBUG("凸包正向ROI结果点数: {}", normal_result->size());
    ASSERT_EQ(normal_result->size(), 4*4*4);

    // 反向ROI测试
    auto inverse_result = alg.compute(box_cube, true);
    SPDLOG_DEBUG("凸包反向ROI结果点数: {}", inverse_result->size());
    ASSERT_EQ(inverse_result->size(), box_cube->size() - 4*4*4);

    // 验证正向+反向 = 原始点云
    ASSERT_EQ(normal_result->size() + inverse_result->size(), box_cube->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 重叠包围盒去重测试)
{
    /*
    测试重叠包围盒的去重逻辑
    */
    algorithm::BoxInfo box1, box2;
    // 创建两个有重叠的包围盒
    box1.lengthx = box1.widthy = box1.depthz = 10;
    box2.lengthx = box2.widthy = box2.depthz = 10;
    box1.translation = Eigen::Vector3f(9, 9, 9);   // 中心在(9,9,9)
    box2.translation = Eigen::Vector3f(11, 11, 11); // 中心在(11,11,11)，有重叠
    box1.quat.x() = box1.quat.y() = box1.quat.z() = 0;
    box1.quat.w() = 1;
    box2.quat.x() = box2.quat.y() = box2.quat.z() = 0;
    box2.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box1, box2});

    // 获取单个包围盒的结果
    algorithm::PcdCropBox single_alg1, single_alg2;
    single_alg1.init_config({box1});
    single_alg2.init_config({box2});
    auto box1_result = single_alg1.compute(box_cube, false);
    auto box2_result = single_alg2.compute(box_cube, false);

    // 获取合并结果
    auto merged_result = alg.compute(box_cube, false);

    SPDLOG_DEBUG("重叠测试 - 包围盒1: {}, 包围盒2: {}, 合并后: {}",
                 box1_result->size(), box2_result->size(), merged_result->size());

    // 验证去重效果：合并结果应该小于简单相加
    ASSERT_LT(merged_result->size(), box1_result->size() + box2_result->size());

    // 验证并集特性：合并结果应该大于等于任意单个结果
    ASSERT_GE(merged_result->size(), box1_result->size());
    ASSERT_GE(merged_result->size(), box2_result->size());

    // 验证完整性：正向 + 反向 = 原始点云
    auto inverse_result = alg.compute(box_cube, true);
    ASSERT_EQ(merged_result->size() + inverse_result->size(), box_cube->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 包围盒反向ROI逻辑验证)
{
    /*
    详细验证包围盒反向ROI的逻辑正确性
    使用简单的单个包围盒进行验证
    */
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 6;  // 较小的包围盒
    box.translation = Eigen::Vector3f(9, 9, 9);  // 中心位置
    box.quat.x() = box.quat.y() = box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});

    // 正向ROI：保留包围盒内的点
    auto normal_result = alg.compute(box_cube, false);
    SPDLOG_DEBUG("单包围盒正向ROI结果点数: {}", normal_result->size());

    // 反向ROI：保留包围盒外的点
    auto inverse_result = alg.compute(box_cube, true);
    SPDLOG_DEBUG("单包围盒反向ROI结果点数: {}", inverse_result->size());

    // 关键验证：正向 + 反向 = 原始点云（无重叠情况）
    ASSERT_EQ(normal_result->size() + inverse_result->size(), box_cube->size());

    // 验证反向ROI确实保留了包围盒外的点
    ASSERT_GT(inverse_result->size(), 0);
    ASSERT_LT(inverse_result->size(), box_cube->size());

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 多包围盒反向ROI性能测试)
{
    /*
    测试多包围盒反向ROI的性能优化效果
    使用indices避免中间点云拷贝
    */

    // 创建多个包围盒进行性能测试
    std::vector<algorithm::BoxInfo> multiple_boxes;
    for (int i = 0; i < 5; ++i) {
        algorithm::BoxInfo box;
        box.lengthx = box.widthy = box.depthz = 4;
        box.translation = Eigen::Vector3f(i*3, i*3, i*3);  // 分散的包围盒
        box.quat.x() = box.quat.y() = box.quat.z() = 0;
        box.quat.w() = 1;
        multiple_boxes.push_back(box);
    }

    algorithm::PcdCropBox alg;
    alg.init_config(multiple_boxes);

    // 测试反向ROI性能
    auto start_time = std::chrono::high_resolution_clock::now();
    auto inverse_result = alg.compute(box_cube, true);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    SPDLOG_DEBUG("多包围盒反向ROI耗时: {} 微秒", duration.count());
    SPDLOG_DEBUG("多包围盒反向ROI结果点数: {}", inverse_result->size());

    // 验证结果正确性
    ASSERT_GT(inverse_result->size(), 0);
    ASSERT_LT(inverse_result->size(), box_cube->size());

    // 验证逐步过滤的正确性：手动验证前两个包围盒的结果
    algorithm::PcdCropBox single_alg1, single_alg2;
    single_alg1.init_config({multiple_boxes[0]});
    single_alg2.init_config({multiple_boxes[1]});

    auto after_box1 = single_alg1.compute(box_cube, true);
    auto after_box2 = single_alg2.compute(after_box1, true);

    // 前两步的结果应该包含在最终结果中（因为最终结果还要过滤更多包围盒）
    ASSERT_LE(inverse_result->size(), after_box2->size());

    SUCCEED();
}

// ==================== 边界情况和异常测试 ====================

TEST_F(PcdPreprocessTest, 空点云测试)
{
    /*测试空点云输入的处理*/
    pcl::PointCloud<pcl::PointXYZ>::Ptr empty_cloud = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();

    // 测试XYZ范围裁剪
    algorithm::PcdCropMinmaxXYZ xyz_alg;
    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 0;
    cfg.max_x = 10;
    xyz_alg.init_config(cfg);
    auto xyz_result = xyz_alg.compute(empty_cloud);
    ASSERT_EQ(xyz_result->size(), 0);

    // 测试包围盒裁剪
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(0, 0, 0);
    box.quat.x() = box.quat.y() = box.quat.z() = 0;
    box.quat.w() = 1;
    algorithm::PcdCropBox box_alg;
    box_alg.init_config({box});
    auto box_result = box_alg.compute(empty_cloud);
    ASSERT_EQ(box_result->size(), 0);

    // 测试凸包裁剪
    std::vector<pcl::PointXYZ> hullPoints{{0, 0, 0}, {1, 0, 0}, {0, 1, 0}, {0, 0, 1}};
    algorithm::PcdCropHull hull_alg;
    hull_alg.init_config(hullPoints);
    auto hull_result = hull_alg.compute(empty_cloud);
    ASSERT_EQ(hull_result->size(), 0);

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 无效配置测试)
{
    /*测试无效配置的处理*/

    // 测试空包围盒配置
    algorithm::PcdCropBox box_alg;
    std::vector<algorithm::BoxInfo> empty_boxes;
    box_alg.init_config(empty_boxes);
    auto box_result = box_alg.compute(minmax_cube);
    ASSERT_EQ(box_result, minmax_cube);  // 应该返回原始点云

    // 测试ROI点数不足的凸包
    algorithm::PcdCropHull hull_alg;
    std::vector<pcl::PointXYZ> insufficient_points{{0, 0, 0}, {1, 0, 0}};  // 只有2个点
    hull_alg.init_config(insufficient_points);
    auto hull_result = hull_alg.compute(minmax_cube);
    ASSERT_EQ(hull_result, minmax_cube);  // 应该返回原始点云

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 极端反向ROI测试)
{
    /*测试极端情况下的反向ROI*/

    // 测试完全不相交的范围（反向ROI应该返回全部点云）
    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 100;  // 远超点云范围
    cfg.max_x = 200;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);

    auto normal_result = alg.compute(minmax_cube, false);
    auto inverse_result = alg.compute(minmax_cube, true);

    ASSERT_EQ(normal_result->size(), 0);  // 正向ROI应该为空
    ASSERT_EQ(inverse_result->size(), minmax_cube->size());  // 反向ROI应该是全部点云

    SUCCEED();
}


int main(int argc, char *argv[])
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}