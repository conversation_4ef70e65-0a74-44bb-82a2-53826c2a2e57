#include "PcdPreprocess.hpp"

#include <chrono>
#include <spdlog/spdlog.h>
#include <gtest/gtest.h>
#include <pcl/io/ply_io.h>


/*
 * 以下全部单元测试约定点云的座标都为偶数，限制值都为奇数
 */
class PcdPreprocessTest : public ::testing::Test
{
protected:

    pcl::PointCloud<pcl::PointXYZ>::Ptr minmax_cube = pcl::PointCloud<pcl::PointXYZ>().makeShared();
    pcl::PointCloud<pcl::PointXYZ>::Ptr box_cube = pcl::PointCloud<pcl::PointXYZ>().makeShared();

    const int POINT_DISTANCE = 2;
    const int MINMAX_CUBE_EDGE_POINT_COUNT = 7;
    const int BOX_CUBE_EDGE_POINT_COUNT = 10;


    void SetUp() override
    {
        spdlog::set_level(spdlog::level::debug);

        for (int x = 0; x < MINMAX_CUBE_EDGE_POINT_COUNT; ++x) {
            for (int y = 0; y < MINMAX_CUBE_EDGE_POINT_COUNT; ++y) {
                for (int z = 0; z < MINMAX_CUBE_EDGE_POINT_COUNT; ++z) {
                    minmax_cube->push_back(pcl::PointXYZ(x*POINT_DISTANCE, y*POINT_DISTANCE, z*POINT_DISTANCE));
                }
            }
        }

        for (int x = 0; x < BOX_CUBE_EDGE_POINT_COUNT; ++x) {
            for (int y = 0; y < BOX_CUBE_EDGE_POINT_COUNT; ++y) {
                for (int z = 0; z < BOX_CUBE_EDGE_POINT_COUNT; ++z) {
                    box_cube->push_back(pcl::PointXYZ(x*POINT_DISTANCE, y*POINT_DISTANCE, z*POINT_DISTANCE));
                }
            }
        }

    }
};

TEST_F(PcdPreprocessTest, X最小限制值前无点)
{
    /*
    源点云：             [X边界:0]---------------[X边界:12]
    参数：    [X min:-1]----------------------------------------------[正无穷]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -1;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最小限制值前有点)
{
    /*
    源点云：             [X边界:0]---------------[X边界:12]
    参数：                            [X min:11]--------------------[正无穷]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 11;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 1);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最大限制值后无点)
{
    /*
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------------------------------[X max:13]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.max_x = 13;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X最大限制值后有点)
{
    /*
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------[X max:3]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.max_x = 3;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 2);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云在X限制区间后)
{
    /*
    源点云：                               [X边界:0]---------------[X边界:12]
    参数：    [X min:-3]--------[X max:-1]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -3;
    cfg.max_x = -1;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间与源点云有交集1)
{
    /*
    源点云：              [X边界:0]---------------[X边界:12]
    参数：    [X min:-1]------------[X max:5]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = -1;
    cfg.max_x = 5;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 3);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间与源点云有交集2)
{
    /*
    源点云：         [X边界:0]---------------[X边界:12]
    参数：                     [X min:5]--------------[X max:14]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 5;
    cfg.max_x = 13;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 4);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, X限制区间在源点云X区间内)
{
    /*
    源点云：         [X边界:0]----------------------------[X边界:12]
    参数：                     [X min:1]-----[X max:11]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 1;
    cfg.max_x = 11;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*MINMAX_CUBE_EDGE_POINT_COUNT * 5);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云在X限制区间前)
{
    /*
    源点云：      [X边界:0]---------------[X边界:12]
    参数：                                           [X min:13]--------[X max:15]
    */

    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    cfg.min_x = 13;
    cfg.max_x = 15;
    algorithm::PcdCropMinmaxXYZ alg;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 没有任何限制)
{
    algorithm::PcdCropMinmaxXYZ alg;
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), minmax_cube->size());
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 同时限制多个维度)
{
    /*同时限制X、Y*/
    algorithm::PcdCropMinmaxXYZ alg;
    algorithm::PcdCropMinmaxXYZConfigurable cfg;
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    alg.init_config(cfg);
    auto result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*2*MINMAX_CUBE_EDGE_POINT_COUNT);

    /*同时限制X、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*MINMAX_CUBE_EDGE_POINT_COUNT*3);

    /*同时限制Y、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), MINMAX_CUBE_EDGE_POINT_COUNT*2*3);

    /*同时限制X、Y、Z*/
    cfg = algorithm::PcdCropMinmaxXYZConfigurable{};
    /*X区间内有1个点*/
    cfg.min_x = 1;
    cfg.max_x = 3;
    /*Y区间内有2个点*/
    cfg.min_y = 1;
    cfg.max_y = 5;
    /*Z区间内有3个点*/
    cfg.min_z = 1;
    cfg.max_z = 7;
    alg.init_config(cfg);
    result = alg.compute(minmax_cube);
    SPDLOG_DEBUG("crop minmax result point size: {}",result->size());
    ASSERT_EQ(result->size(), 1*2*3);

    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒外)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(100, 100, 100);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;


    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    ASSERT_EQ(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云部分在包围盒内)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(19, 19, 19);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 2*2*2);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒内且包围盒无旋转)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(9, 9, 9);
    box.quat.x() = 0;
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 4*4*4);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 源点云全部在包围盒内且包围盒带旋转)
{
    algorithm::BoxInfo box;
    box.lengthx = box.widthy = box.depthz = 8;
    box.translation = Eigen::Vector3f(9, 9, 9);
    box.quat.x() = 0.3826834;/*绕X轴旋转45度*/
    box.quat.y() = 0;
    box.quat.z() = 0;
    box.quat.w() = 0.9238795;

    algorithm::PcdCropBox alg;
    alg.init_config({box});
    auto result = alg.compute(box_cube);
    SPDLOG_DEBUG("cropbox result point size: {}",result->size());
    // pcl::io::savePLYFileBinary("result.ply", *result);
    ASSERT_EQ(result->size(), 48);
    SUCCEED();
}

TEST_F(PcdPreprocessTest, 多个包围盒)
{
    algorithm::BoxInfo box1,box2;
    box1.lengthx = box1.widthy = box1.depthz = box2.lengthx = box2.widthy = box2.depthz = 8;
    box1.translation = Eigen::Vector3f(9, 9, 9);
    box2.translation = Eigen::Vector3f(13, 13, 13);
    box1.quat.x() = box2.quat.x() = 0;
    box1.quat.y() = box2.quat.y() = 0;
    box1.quat.z() = box2.quat.z() = 0;
    box1.quat.w() = box2.quat.w() = 1;

    algorithm::PcdCropBox alg;
    alg.init_config({box1,box2});
    auto results = alg.computeOutputList(box_cube);
    ASSERT_EQ(results.size(), 2);
    SPDLOG_DEBUG("cropbox result 1 point size: {},result 2 point size:{}",results[0]->size(),results[1]->size());
    ASSERT_EQ(results[0]->size(), 4*4*4);
    ASSERT_EQ(results[1]->size(), 4*4*4);

    auto mergeResult = alg.compute(box_cube);
    SPDLOG_DEBUG("multi cropbox merge result point size: {}",mergeResult->size());
    ASSERT_EQ(mergeResult->size(), 4*4*4 + 4*4*4);

    SUCCEED();
}

// TEST_F(PcdPreprocessTest, 凸包1)
// {
//     /*参考上面box的测试case，构造一个8*8*8的小正方体凸包，其中心点与点云正方体的中心点重合 */
//     std::vector<pcl::PointXYZ> hullPoints{
//         {5, 5, 5},
//         {13, 5, 5},
//         {13, 13, 5},
//         {5, 13, 5},
//         {5, 5, 13},
//         {13, 5, 13},
//         {13, 13, 13},
//         {5, 13, 13},
//     };
//
//     algorithm::PcdCropHull alg;
//     alg.init_config(hullPoints);
//     auto result = alg.compute(box_cube);
//     SPDLOG_DEBUG("crophull result point size: {}",result->size());
//     ASSERT_EQ(result->size(), 4*4*4);
//     SUCCEED();
// }


int main(int argc, char *argv[])
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}