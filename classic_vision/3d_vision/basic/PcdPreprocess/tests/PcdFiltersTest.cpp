#include "PcdFilters.hpp"

#include <chrono>
#include <spdlog/spdlog.h>
#include <gtest/gtest.h>
#include <pcl/io/ply_io.h>
#include <pcl/io/pcd_io.h>
#include <pcl/common/common.h>
#include <random>
#include <iomanip>
#include <fstream>
#include <sys/resource.h>

/**
 * PcdFilters测试类
 * 测试各种点云滤波算法的功能
 */
class PcdFiltersTest : public ::testing::Test
{
protected:
    // 使用静态变量确保数据在所有测试间共享
    static pcl::PointCloud<pcl::PointXYZ>::Ptr test_cloud;
    static pcl::PointCloud<pcl::PointXYZ>::Ptr dense_cloud;
    static pcl::PointCloud<pcl::PointXYZ>::Ptr outlier_cloud;
    static pcl::PointCloud<pcl::PointXYZ>::Ptr cluster_cloud;

    // 百万级别的测试数据
    const int GRID_SIZE = 100;  // 100x100x100 = 1,000,000 points
    const float POINT_SPACING = 0.01f;  // 更小的间距
    const int DENSE_CLOUD_SIZE = 1000000;  // 100万点
    const int OUTLIER_CLOUD_SIZE = 100000;  // 10万点用于离群点测试
    const int CLUSTER_CLOUD_SIZE = 50000;   // 5万点用于聚类测试

    // 静态标志，确保数据只创建一次
    static bool data_initialized;

    void SetUp() override
    {
        spdlog::set_level(spdlog::level::info);  // 改为info级别，减少日志输出

        // 如果数据已经初始化，直接返回
        if (data_initialized) {
            SPDLOG_INFO("使用已创建的测试数据: test_cloud={}, dense_cloud={}, outlier_cloud={}, cluster_cloud={}",
                       test_cloud->size(), dense_cloud->size(), outlier_cloud->size(), cluster_cloud->size());
            return;
        }

        SPDLOG_INFO("开始创建百万级别测试点云数据...");
        auto start_time = std::chrono::high_resolution_clock::now();

        // 创建规则网格点云用于下采样测试 (100x100x100 = 1,000,000 points)
        SPDLOG_INFO("创建规则网格点云: {}x{}x{} = {} 点", GRID_SIZE, GRID_SIZE, GRID_SIZE, GRID_SIZE*GRID_SIZE*GRID_SIZE);
        test_cloud->reserve(GRID_SIZE * GRID_SIZE * GRID_SIZE);
        for (int x = 0; x < GRID_SIZE; ++x) {
            for (int y = 0; y < GRID_SIZE; ++y) {
                for (int z = 0; z < GRID_SIZE; ++z) {
                    test_cloud->push_back(pcl::PointXYZ(x * POINT_SPACING, y * POINT_SPACING, z * POINT_SPACING));
                }
            }
        }

        // 创建密集随机点云用于下采样测试 (1,000,000 points)
        SPDLOG_INFO("创建密集随机点云: {} 点", DENSE_CLOUD_SIZE);
        std::random_device rd;
        std::mt19937 gen(42);  // 使用固定种子确保可重复性
        std::uniform_real_distribution<float> dis(0.0f, 10.0f);  // 扩大范围

        dense_cloud->reserve(DENSE_CLOUD_SIZE);
        for (int i = 0; i < DENSE_CLOUD_SIZE; ++i) {
            dense_cloud->push_back(pcl::PointXYZ(dis(gen), dis(gen), dis(gen)));
        }

        // 创建带离群点的点云 (100,000 points)
        SPDLOG_INFO("创建带离群点的点云: {} 点", OUTLIER_CLOUD_SIZE);
        outlier_cloud->reserve(OUTLIER_CLOUD_SIZE);
        // 主要点云集中在原点附近 (95%)
        std::uniform_real_distribution<float> main_dis(-1.0f, 1.0f);
        for (int i = 0; i < OUTLIER_CLOUD_SIZE * 0.95; ++i) {
            outlier_cloud->push_back(pcl::PointXYZ(main_dis(gen), main_dis(gen), main_dis(gen)));
        }
        // 添加离群点 (5%)
        std::uniform_real_distribution<float> outlier_dis(10.0f, 20.0f);
        for (int i = 0; i < OUTLIER_CLOUD_SIZE * 0.05; ++i) {
            outlier_cloud->push_back(pcl::PointXYZ(outlier_dis(gen), outlier_dis(gen), outlier_dis(gen)));
        }

        // 创建聚类测试点云 (50,000 points)
        SPDLOG_INFO("创建聚类测试点云: {} 点", CLUSTER_CLOUD_SIZE);
        cluster_cloud->reserve(CLUSTER_CLOUD_SIZE);

        // 第一个簇 (40%)
        std::uniform_real_distribution<float> cluster1_dis(-1.0f, 1.0f);
        for (int i = 0; i < CLUSTER_CLOUD_SIZE * 0.4; ++i) {
            cluster_cloud->push_back(pcl::PointXYZ(cluster1_dis(gen), cluster1_dis(gen), cluster1_dis(gen)));
        }

        // 第二个簇 (40%)
        std::uniform_real_distribution<float> cluster2_dis(8.0f, 10.0f);
        for (int i = 0; i < CLUSTER_CLOUD_SIZE * 0.4; ++i) {
            cluster_cloud->push_back(pcl::PointXYZ(cluster2_dis(gen), cluster2_dis(gen), cluster2_dis(gen)));
        }

        // 第三个簇 (15%)
        std::uniform_real_distribution<float> cluster3_dis(15.0f, 17.0f);
        for (int i = 0; i < CLUSTER_CLOUD_SIZE * 0.15; ++i) {
            cluster_cloud->push_back(pcl::PointXYZ(cluster3_dis(gen), cluster3_dis(gen), cluster3_dis(gen)));
        }

        // 添加孤立点 (5%)
        std::uniform_real_distribution<float> isolated_dis(25.0f, 30.0f);
        for (int i = 0; i < CLUSTER_CLOUD_SIZE * 0.05; ++i) {
            cluster_cloud->push_back(pcl::PointXYZ(isolated_dis(gen), isolated_dis(gen), isolated_dis(gen)));
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        SPDLOG_INFO("测试数据创建完成，耗时: {} ms", duration.count());
        SPDLOG_INFO("总点云数量: test_cloud={}, dense_cloud={}, outlier_cloud={}, cluster_cloud={}",
                   test_cloud->size(), dense_cloud->size(), outlier_cloud->size(), cluster_cloud->size());

        // 标记数据已初始化
        data_initialized = true;
    }
};

// 定义静态变量
bool PcdFiltersTest::data_initialized = false;
pcl::PointCloud<pcl::PointXYZ>::Ptr PcdFiltersTest::test_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();
pcl::PointCloud<pcl::PointXYZ>::Ptr PcdFiltersTest::dense_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();
pcl::PointCloud<pcl::PointXYZ>::Ptr PcdFiltersTest::outlier_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();
pcl::PointCloud<pcl::PointXYZ>::Ptr PcdFiltersTest::cluster_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();

TEST_F(PcdFiltersTest, 体素滤波下采样)
{
    SPDLOG_INFO("=== 体素滤波下采样性能测试 ===");

    // 使用适中的体素大小来确保下采样效果
    float voxel_size = 0.1f;  // 适中的体素大小

    auto start_time = std::chrono::high_resolution_clock::now();
    auto result = algorithm::cloudfilter::voxelDownSample(dense_cloud, voxel_size);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double compression_ratio = (double)result->size() / dense_cloud->size();
    double processing_speed = (double)dense_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 体素滤波后大小: {}", dense_cloud->size(), result->size());
    SPDLOG_INFO("压缩比: {:.1f}%, 处理时间: {} ms", (1.0 - compression_ratio) * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);

    // 体素滤波应该减少点的数量
    ASSERT_LT(result->size(), dense_cloud->size());
    ASSERT_GT(result->size(), 0);
    ASSERT_LT(compression_ratio, 0.8);  // 压缩比应该小于80%
    SUCCEED();
}

TEST_F(PcdFiltersTest, 均匀采样下采样)
{
    SPDLOG_INFO("=== 均匀采样下采样性能测试 ===");

    float radius = 0.1f;  // 适中的搜索半径

    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result = uniformDownSample(dense_cloud, radius);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double compression_ratio = (double)result->size() / dense_cloud->size();
    double processing_speed = (double)dense_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 均匀采样后大小: {}", dense_cloud->size(), result->size());
    SPDLOG_INFO("压缩比: {:.1f}%, 处理时间: {} ms", (1.0 - compression_ratio) * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);

    // 均匀采样应该减少点的数量
    ASSERT_LT(result->size(), dense_cloud->size());
    ASSERT_GT(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdFiltersTest, 近似体素滤波下采样)
{
    SPDLOG_INFO("=== 近似体素滤波下采样性能测试 ===");

    float voxel_size = 0.1f;  // 与精确体素滤波相同的大小进行对比

    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result = approximateVoxelDownSample(dense_cloud, voxel_size);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double compression_ratio = (double)result->size() / dense_cloud->size();
    double processing_speed = (double)dense_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 近似体素滤波后大小: {}", dense_cloud->size(), result->size());
    SPDLOG_INFO("压缩比: {:.1f}%, 处理时间: {} ms", (1.0 - compression_ratio) * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);

    // 近似体素滤波应该减少点的数量
    ASSERT_LT(result->size(), dense_cloud->size());
    ASSERT_GT(result->size(), 0);
    SUCCEED();
}

TEST_F(PcdFiltersTest, 统计滤波去除离群点)
{
    SPDLOG_INFO("=== 统计滤波去除离群点性能测试 ===");

    int mean_k = 20;  // 增加邻居数以适应大数据集
    double std_dev_mul_thresh = 2.0;  // 适中的阈值

    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result = removeOutliersStatistical(outlier_cloud, mean_k, std_dev_mul_thresh);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double outlier_ratio = 1.0 - (double)result->size() / outlier_cloud->size();
    double processing_speed = (double)outlier_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 统计滤波后大小: {}", outlier_cloud->size(), result->size());
    SPDLOG_INFO("离群点比例: {:.1f}%, 处理时间: {} ms", outlier_ratio * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);

    // 统计滤波应该去除离群点，减少点的数量
    ASSERT_LT(result->size(), outlier_cloud->size());
    ASSERT_GT(result->size(), 0);
    ASSERT_GT(outlier_ratio, 0.01);  // 至少应该去除1%的点
    SUCCEED();
}

TEST_F(PcdFiltersTest, 半径滤波去除离群点)
{
    SPDLOG_INFO("=== 半径滤波去除离群点性能测试 ===");

    // 调整参数以确保能检测到离群点
    float radius = 0.5f;  // 较小的搜索半径，更容易检测离群点
    int min_neighbors = 20;  // 增加最小邻居数要求

    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result = removeOutliersRadiusSearch(outlier_cloud, radius, min_neighbors);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double outlier_ratio = 1.0 - (double)result->size() / outlier_cloud->size();
    double processing_speed = (double)outlier_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 半径滤波后大小: {}", outlier_cloud->size(), result->size());
    SPDLOG_INFO("离群点比例: {:.1f}%, 处理时间: {} ms", outlier_ratio * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);
    SPDLOG_INFO("参数: radius={:.1f}, min_neighbors={}", radius, min_neighbors);

    // 半径滤波应该去除离群点
    ASSERT_GT(result->size(), 0);
    // 如果没有检测到离群点，说明参数需要调整，但不应该失败测试
    if (result->size() == outlier_cloud->size()) {
        SPDLOG_WARN("半径滤波未检测到离群点，可能需要调整参数");
    }
    SUCCEED();
}

TEST_F(PcdFiltersTest, 欧式聚类去除离群点)
{
    SPDLOG_INFO("=== 欧式聚类去除离群点性能测试 ===");

    // 使用较小的数据集进行欧式聚类测试，因为算法复杂度高
    pcl::PointCloud<pcl::PointXYZ>::Ptr small_cluster_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();

    // 从cluster_cloud中采样5000个点
    size_t sample_size = 5000;
    size_t step = cluster_cloud->size() / sample_size;
    for (size_t i = 0; i < cluster_cloud->size(); i += step) {
        small_cluster_cloud->push_back(cluster_cloud->points[i]);
        if (small_cluster_cloud->size() >= sample_size) break;
    }

    double cluster_tolerance = 1.0;  // 调整聚类容差
    int min_cluster_size = 1000;      // 降低最小簇大小
    int max_cluster_size = 10000;     // 调整最大簇大小

    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result = removeOutliersEuclideanCluster(small_cluster_cloud, cluster_tolerance, min_cluster_size, max_cluster_size);
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double retention_ratio = (double)result->size() / small_cluster_cloud->size();
    double processing_speed = (double)small_cluster_cloud->size() / duration.count();  // 点/ms

    SPDLOG_INFO("原始点云大小: {}, 欧式聚类后大小: {}", small_cluster_cloud->size(), result->size());
    SPDLOG_INFO("保留比例: {:.1f}%, 处理时间: {} ms", retention_ratio * 100.0, duration.count());
    SPDLOG_INFO("处理速度: {:.0f} 点/ms", processing_speed);
    SPDLOG_INFO("参数: tolerance={:.1f}, min_size={}, max_size={}", cluster_tolerance, min_cluster_size, max_cluster_size);

    // 欧式聚类应该保留主要的簇
    ASSERT_GT(result->size(), 0);
    // 如果没有检测到孤立点，说明参数需要调整，但不应该失败测试
    if (result->size() == small_cluster_cloud->size()) {
        SPDLOG_WARN("欧式聚类未检测到孤立点，可能需要调整参数");
    }
    SUCCEED();
}

TEST_F(PcdFiltersTest, 最小二乘法平滑)
{
    SPDLOG_INFO("=== 最小二乘法平滑性能测试 ===");

    // 使用较小的点云子集进行MLS测试，因为MLS计算量很大
    pcl::PointCloud<pcl::PointXYZ>::Ptr small_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();

    // 从dense_cloud中采样5000个点，并确保点之间有合理的间距
    size_t sample_size = 5000;
    size_t step = dense_cloud->size() / sample_size;
    for (size_t i = 0; i < dense_cloud->size(); i += step) {
        small_cloud->push_back(dense_cloud->points[i]);
        if (small_cloud->size() >= sample_size) break;
    }

    // 测试1: 仅平滑，不上采样
    auto start_time = std::chrono::high_resolution_clock::now();
    using namespace algorithm::cloudfilter;
    auto result_smooth = smoothMLS(small_cloud, 0.3, 0, 0);  // 仅平滑
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration_smooth = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double processing_speed_smooth = (double)small_cloud->size() / duration_smooth.count();

    SPDLOG_INFO("=== MLS仅平滑测试 ===");
    SPDLOG_INFO("原始点云大小: {}, MLS平滑后大小: {}", small_cloud->size(), result_smooth->size());
    SPDLOG_INFO("处理时间: {} ms, 处理速度: {:.0f} 点/ms", duration_smooth.count(), processing_speed_smooth);

    // 测试2: 平滑+上采样（使用更激进的上采样参数）
    start_time = std::chrono::high_resolution_clock::now();
    auto result_upsample = smoothMLS(small_cloud, 0.3, 0.1, 0.05);  // 更小的参数，更密集的上采样
    end_time = std::chrono::high_resolution_clock::now();

    auto duration_upsample = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    double processing_speed_upsample = (double)small_cloud->size() / duration_upsample.count();

    SPDLOG_INFO("=== MLS平滑+上采样测试 ===");
    SPDLOG_INFO("原始点云大小: {}, MLS上采样后大小: {}", small_cloud->size(), result_upsample->size());
    SPDLOG_INFO("处理时间: {} ms, 处理速度: {:.0f} 点/ms", duration_upsample.count(), processing_speed_upsample);

    // 计算保留率和上采样率
    double smooth_retention = (double)result_smooth->size() / small_cloud->size();
    double upsample_ratio = (double)result_upsample->size() / small_cloud->size();

    SPDLOG_INFO("仅平滑保留率: {:.1f}%, 上采样比率: {:.1f}%", smooth_retention * 100.0, upsample_ratio * 100.0);

    // MLS平滑的合理期望
    ASSERT_GT(result_smooth->size(), 0);
    ASSERT_GT(result_upsample->size(), 0);

    SUCCEED();
}

TEST_F(PcdFiltersTest, 体素滤波参数边界测试)
{
    SPDLOG_INFO("=== 体素滤波参数边界测试 ===");

    // 使用较小的点云子集进行边界测试
    pcl::PointCloud<pcl::PointXYZ>::Ptr small_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();
    size_t sample_size = 50000;
    size_t step = dense_cloud->size() / sample_size;
    for (size_t i = 0; i < dense_cloud->size(); i += step) {
        small_cloud->push_back(dense_cloud->points[i]);
        if (small_cloud->size() >= sample_size) break;
    }

    using namespace algorithm::cloudfilter;

    // 测试极小的体素大小
    float small_voxel = 0.01f;
    auto start_time = std::chrono::high_resolution_clock::now();
    auto result_small = voxelDownSample(small_cloud, small_voxel);
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration_small = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // 测试较大的体素大小
    float large_voxel = 1.0f;
    start_time = std::chrono::high_resolution_clock::now();
    auto result_large = voxelDownSample(small_cloud, large_voxel);
    end_time = std::chrono::high_resolution_clock::now();
    auto duration_large = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    double speed_small = (double)small_cloud->size() / duration_small.count();  // 点/ms
    double speed_large = (double)small_cloud->size() / duration_large.count();  // 点/ms

    SPDLOG_INFO("小体素({:.3f}): {} -> {} 点, 耗时: {} ms, 速度: {:.0f} 点/ms",
               small_voxel, small_cloud->size(), result_small->size(), duration_small.count(), speed_small);
    SPDLOG_INFO("大体素({:.1f}): {} -> {} 点, 耗时: {} ms, 速度: {:.0f} 点/ms",
               large_voxel, small_cloud->size(), result_large->size(), duration_large.count(), speed_large);

    ASSERT_GT(result_small->size(), 0);
    ASSERT_GT(result_large->size(), 0);
    ASSERT_LT(result_large->size(), result_small->size());

    SUCCEED();
}

TEST_F(PcdFiltersTest, 空点云处理)
{
    SPDLOG_INFO("=== 空点云处理测试 ===");

    pcl::PointCloud<pcl::PointXYZ>::Ptr empty_cloud = pcl::PointCloud<pcl::PointXYZ>().makeShared();

    // 测试各种滤波器对空点云的处理
    using namespace algorithm::cloudfilter;
    auto voxel_result = voxelDownSample(empty_cloud, 0.1f);
    ASSERT_EQ(voxel_result->size(), 0);

    auto uniform_result = uniformDownSample(empty_cloud, 0.1f);
    ASSERT_EQ(uniform_result->size(), 0);

    auto approx_result = approximateVoxelDownSample(empty_cloud, 0.1f);
    ASSERT_EQ(approx_result->size(), 0);

    SPDLOG_INFO("所有滤波器都正确处理了空点云");
    SUCCEED();
}

int main(int argc, char *argv[])
{
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
