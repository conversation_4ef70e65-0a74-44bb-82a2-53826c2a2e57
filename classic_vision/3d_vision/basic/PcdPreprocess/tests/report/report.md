### 硬件环境
主板：Jetson Orin NX 16GB  
CPU Arch: ARMv8  
CPU Core:8  
GPU Core:1024  
RAM:16GB  

### 软件环境
OS:Nvidia Jetpack 6 / Ubuntu 22.04  
Docker: 26.1.2  
CUDA：12.2  
PCL: 1.14.1  
Eigen：3.4.0  
OpenCV: 4.5.4  

### 测试方案
测试用例设计思路：构建一个形状为长方体的点云，其中含固定数量的点。
每个测试用例设定不同的约束条件，对输出的点云的数量进行断言。
#### 规则ROI
长方体含7 * 7 * 7 = 343个点，其中的点的座标都为偶数，约定约束条件值为基数

有6个维度的约束条件：
- X座标最小值
- X座标最大值
- Y座标最小值
- Y座标最大值
- Z座标最小值
- Z座标最大值

测试用例的排列组合设计：
- 单个约束条件的排列
- 多个约束条件的组合
- 源点云是「全部」「部分」「全不」满足约束条件的排列
- 以上3种因素的组合

规则ROI测试用例清单
- 设定X最小值，且源点云全不在区间  
  期望结果：结果点云为空
   ```text
       源点云：             [X边界:0]---------------[X边界:12]
       参数：    [X min:-1]----------------------------------------------[正无穷]
   ```
- 设定X最小值，且源点云部分在区间  
  期望结果：结果点云的点数少于源点云
   ```text
    源点云：             [X边界:0]---------------[X边界:12]
    参数：                            [X min:11]--------------------[正无穷]
   ```
- 设定X最大值，且源点云全不在区间  
  期望结果：结果点云为空
   ```text
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------------------------------[X max:13]
   ```
- 设定X最大值，且源点云部分在区间  
  期望结果：结果点云的点数少于源点云
   ```text
    源点云：            [X边界:0]---------------[X边界:12]
    参数：    [负无穷]--------------[X max:3]
   ```
- 设定X最小值最大值区间，且源点云全不在区间  
  期望结果：结果点云为空
   ```text
    源点云：                               [X边界:0]---------------[X边界:12]
    参数：    [X min:-3]--------[X max:-1]
   ```
- 设定X最小值最大值区间，且源点云有超出上限的部分，另一部分在区间  
  期望结果：结果点云的点数少于源点云
   ```text
    源点云：              [X边界:0]---------------[X边界:12]
    参数：    [X min:-1]------------[X max:5]
   ```
- 设定X最小值最大值区间，且源点云有超出下限的部分，另一部分在区间  
  期望结果：结果点云的点数少于源点云
   ```text
    源点云：         [X边界:0]---------------[X边界:12]
    参数：                     [X min:5]--------------[X max:14]
   ```
- 设定X最小值最大值区间，且源点云全部在区间  
  期望结果：结果点云的点数与源点云一致
   ```text
    源点云：         [X边界:0]----------------------------[X边界:12]
    参数：                     [X min:1]-----[X max:11]
   ```
- 不设定任何条件，期望输出与源点云一致
- 同时设定XY或XZ或YZ的最大、最小值
- 同时设定XYZ的最大、最小值

#### 包围盒ROI
长方体含10 * 10 * 10 = 1000个点，其中的点的座标都为偶数，约定包围盒顶点座标为基数

包围盒ROI测试用例清单
- 源点云全部在包围盒外，期望输出为空
- 源点云部分在包围盒内，期望输出有特定点数的点云
- 源点云全部在包围盒内，且包围盒无旋转，期望输出有特定点数的点云
- 源点云全部在包围盒内，且包围盒带旋转，期望输出有特定点数的点云
- 设定多个包围盒，期望输出与包围盒数量相同的点云列表


### 测试结果
```text
[==========] Running 16 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 16 tests from PcdPreprocessTest
[ RUN      ] PcdPreprocessTest.X最小限制值前无点
[2024-11-29 09:21:17.696] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 343
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:59] crop minmax result point size: 343
[       OK ] PcdPreprocessTest.X最小限制值前无点 (0 ms)
[ RUN      ] PcdPreprocessTest.X最小限制值前有点
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 49
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:76] crop minmax result point size: 49
[       OK ] PcdPreprocessTest.X最小限制值前有点 (0 ms)
[ RUN      ] PcdPreprocessTest.X最大限制值后无点
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 343
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:93] crop minmax result point size: 343
[       OK ] PcdPreprocessTest.X最大限制值后无点 (0 ms)
[ RUN      ] PcdPreprocessTest.X最大限制值后有点
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 98
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:110] crop minmax result point size: 98
[       OK ] PcdPreprocessTest.X最大限制值后有点 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云在X限制区间后
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 0
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:128] crop minmax result point size: 0
[       OK ] PcdPreprocessTest.源点云在X限制区间后 (0 ms)
[ RUN      ] PcdPreprocessTest.X限制区间与源点云有交集1
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 147
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:146] crop minmax result point size: 147
[       OK ] PcdPreprocessTest.X限制区间与源点云有交集1 (0 ms)
[ RUN      ] PcdPreprocessTest.X限制区间与源点云有交集2
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 196
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:164] crop minmax result point size: 196
[       OK ] PcdPreprocessTest.X限制区间与源点云有交集2 (0 ms)
[ RUN      ] PcdPreprocessTest.X限制区间在源点云X区间内
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 245
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:182] crop minmax result point size: 245
[       OK ] PcdPreprocessTest.X限制区间在源点云X区间内 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云在X限制区间前
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 0
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:200] crop minmax result point size: 0
[       OK ] PcdPreprocessTest.源点云在X限制区间前 (0 ms)
[ RUN      ] PcdPreprocessTest.没有任何限制
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:209] crop minmax result point size: 343
[       OK ] PcdPreprocessTest.没有任何限制 (0 ms)
[ RUN      ] PcdPreprocessTest.同时限制多个维度
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 49
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:47] indices size after filter y: 14
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:227] crop minmax result point size: 14
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 49
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:59] indices size after filter z: 21
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:240] crop minmax result point size: 21
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:47] indices size after filter y: 98
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:59] indices size after filter z: 42
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:253] crop minmax result point size: 42
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:35] indices size after filter x: 49
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:47] indices size after filter y: 14
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:59] indices size after filter z: 6
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:269] crop minmax result point size: 6
[       OK ] PcdPreprocessTest.同时限制多个维度 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云全部在包围盒外
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 0
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:118] filtered point size: 0
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:289] cropbox result point size: 0
[       OK ] PcdPreprocessTest.源点云全部在包围盒外 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云部分在包围盒内
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 8
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:118] filtered point size: 8
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:307] cropbox result point size: 8
[       OK ] PcdPreprocessTest.源点云部分在包围盒内 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云全部在包围盒内且包围盒无旋转
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 64
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:118] filtered point size: 64
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:326] cropbox result point size: 64
[       OK ] PcdPreprocessTest.源点云全部在包围盒内且包围盒无旋转 (0 ms)
[ RUN      ] PcdPreprocessTest.源点云全部在包围盒内且包围盒带旋转
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 48
[2024-11-29 09:21:17.697] [debug] [PcdPreprocess.cpp:118] filtered point size: 48
[2024-11-29 09:21:17.697] [debug] [PcdPreprocessTest.cpp:345] cropbox result point size: 48
[       OK ] PcdPreprocessTest.源点云全部在包围盒内且包围盒带旋转 (0 ms)
[ RUN      ] PcdPreprocessTest.多个包围盒
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 64
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:150] filtered pointcloud2 point size: 64
[2024-11-29 09:21:17.698] [debug] [PcdPreprocessTest.cpp:366] cropbox result 1 point size: 64,result 2 point size:64
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:135] input point size: 1000
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:150] filtered pointcloud1 point size: 64
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:150] filtered pointcloud2 point size: 64
[2024-11-29 09:21:17.698] [debug] [PcdPreprocess.cpp:118] filtered point size: 128
[2024-11-29 09:21:17.698] [debug] [PcdPreprocessTest.cpp:371] multi cropbox merge result point size: 128
[       OK ] PcdPreprocessTest.多个包围盒 (0 ms)
[----------] 16 tests from PcdPreprocessTest (1 ms total)

[----------] Global test environment tear-down
[==========] 16 tests from 1 test suite ran. (1 ms total)
[  PASSED  ] 16 tests.
```
