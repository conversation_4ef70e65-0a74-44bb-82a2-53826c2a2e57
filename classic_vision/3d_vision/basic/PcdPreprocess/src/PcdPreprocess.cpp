/**
 * @file PcdPreprocess.cpp
 * @brief 点云预处理模块实现
 * @details 提供三种点云裁剪功能：
 *          1. PcdCropMinmaxXYZ - 基于XYZ坐标范围的点云裁剪
 *          2. PcdCropBox - 基于包围盒的点云裁剪
 *          3. PcdCropHull - 基于凸包的点云裁剪
 * <AUTHOR> Team
 * @date 2024
 */

#include "PcdPreprocess.hpp"
#include <spdlog/spdlog.h>
#include <pcl/common/common.h>
#include <pcl/io/ply_io.h>
#include <yaml-cpp/yaml.h>

#include <Eigen/Dense>
#include <iostream>
#include <pcl/common/transforms.h>
#include <pcl/filters/passthrough.h>
#include "pcl/filters/crop_box.h"
#include "pcl/filters/crop_hull.h"
#include "pcl/keypoints/harris_3d.h"
#include "pcl/surface/convex_hull.h"

namespace algorithm {
  void PcdCropMinmaxXYZ::init_config(const PcdCropMinmaxXYZConfigurable& cfg)
  {
    // 保存XYZ范围裁剪配置参数
    this->cfg = cfg;
    SPDLOG_DEBUG("初始化XYZ范围裁剪配置: x[{}, {}], y[{}, {}], z[{}, {}]",
                 cfg.min_x, cfg.max_x, cfg.min_y, cfg.max_y, cfg.min_z, cfg.max_z);
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropMinmaxXYZ::compute(pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse)
  {
    // 检查输入参数有效性
    if (!input || input->empty()) {
      SPDLOG_WARN("输入点云为空或无效");
      return std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    }

    // 创建直通滤波器
    pcl::PassThrough<pcl::PointXYZ> pass;
    pass.setInputCloud(input);
    pass.setNegative(inverse);  // 设置是否反向ROI
    pcl::IndicesPtr indices = std::make_shared<pcl::Indices>();
    bool hasFiltering = false;

    // 定义滤波参数结构：字段名、最小值、最大值
    struct FilterParams {
      const char* field_name;
      float min_val;
      float max_val;
    };

    // 按XYZ顺序定义滤波参数
    FilterParams filters[] = {
      {"x", cfg.min_x, cfg.max_x},
      {"y", cfg.min_y, cfg.max_y},
      {"z", cfg.min_z, cfg.max_z}
    };

    // 依次应用各轴滤波
    for (const auto& filter : filters) {
      // 检查是否需要在该轴上进行滤波
      if (filter.max_val != FLT_MAX || filter.min_val != -FLT_MAX) {
        if (hasFiltering) {
          // 将前一次滤波结果作为本次输入
          pass.setIndices(indices);
        }
        pass.setFilterFieldName(filter.field_name);
        pass.setFilterLimits(filter.min_val, filter.max_val);
        pass.filter(*indices);
        SPDLOG_DEBUG("{}轴{}滤波后索引数量: {}", filter.field_name, inverse ? "反向" : "正向", indices->size());
        hasFiltering = true;
      }
    }

    // 根据滤波结果返回相应点云
    if (hasFiltering && indices->size() != input->size()) {
      // 有实际滤波且点数发生变化，返回滤波后的点云
      pcl::PointCloud<pcl::PointXYZ>::Ptr result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      pcl::copyPointCloud(*input, *indices, *result);
      SPDLOG_DEBUG("{}滤波完成，原始点数: {}, 滤波后点数: {}", inverse ? "反向" : "正向", input->size(), result->size());
      return result;
    } else {
      // 无滤波或点数未变化，返回原始点云
      return input;
    }
  }

  void PcdCropMinmaxXYZ::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse){
      // 调用主计算函数并将结果赋值给输出参数
      output = compute(input, inverse);
  }

  void PcdCropBox::init_config(const std::vector<algorithm::BoxInfo>& cfg)
  {
    // 保存包围盒配置信息
    this->boxes = cfg;
    SPDLOG_DEBUG("初始化包围盒配置，包围盒数量: {}", cfg.size());
  }

  /**
   * 获取包围盒裁剪参数：最小/最大边界、旋转和平移
   * @param box 用户定义的包围盒数据
   * @return 包含最小边界、最大边界、平移和旋转的元组
   */
  std::tuple<Eigen::Vector4f,Eigen::Vector4f,Eigen::Vector3f,Eigen::Vector3f> get_box_filter(const BoxInfo& box)
  {
    // min和max表示未经变换（平移+旋转）前的长方体对角线端点
    // 通过三条边长计算得出
    Eigen::Vector4f min;
    Eigen::Vector4f max;
    min[0] = -box.lengthx / 2;  // X轴负方向边界
    min[1] = -box.widthy / 2;   // Y轴负方向边界
    min[2] = -box.depthz / 2;   // Z轴负方向边界
    min[3] = 1;                 // 齐次坐标
    max[0] = box.lengthx / 2;   // X轴正方向边界
    max[1] = box.widthy / 2;    // Y轴正方向边界
    max[2] = box.depthz / 2;    // Z轴正方向边界
    max[3] = 1;                 // 齐次坐标

    // 将四元数转换为旋转矩阵
    Eigen::Quaternion<float> rotate_quat(box.quat.w(),box.quat.x(),box.quat.y(),box.quat.z());
    // 将旋转矩阵转换为欧拉角（Z-Y-X顺序）
    Eigen::Vector3f eulerAngle = rotate_quat.matrix().eulerAngles(2, 1, 0);
    // CropBox使用(rx,ry,rz)顺序而不是(rz,ry,rx)
    Eigen::Vector3f rotation = Eigen::Vector3f(eulerAngle(2), eulerAngle(1), eulerAngle(0));

    return std::make_tuple(min, max, box.translation, rotation);
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse) {
    // 检查输入参数有效性
    if (!input || input->empty()) {
      SPDLOG_WARN("输入点云为空或无效");
      return std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    }

    if (boxes.empty()) {
      SPDLOG_WARN("包围盒配置为空，返回原始点云");
      return input;
    }

    if (inverse) {
      // 反向ROI：使用indices逐步过滤，避免中间点云拷贝，提高性能
      // 优势：只在最后一步进行点云拷贝，中间过程只操作索引
      pcl::CropBox<pcl::PointXYZ> crop_box;
      crop_box.setInputCloud(input);
      crop_box.setNegative(true);  // 反向：保留包围盒外的点
      pcl::IndicesPtr indices = std::make_shared<pcl::Indices>();
      bool hasFiltering = false;

      for (size_t i = 0; i < boxes.size(); ++i) {
        const auto& box = boxes[i];
        auto [min, max, translation, rotation] = get_box_filter(box);

        if (hasFiltering) {
          // 将前一次过滤结果作为本次输入
          crop_box.setIndices(indices);
        }

        // 设置包围盒参数
        crop_box.setMin(min);
        crop_box.setMax(max);
        crop_box.setTranslation(translation);
        crop_box.setRotation(rotation);

        // 执行裁剪，更新indices
        crop_box.filter(*indices);
        SPDLOG_DEBUG("第{}个包围盒反向裁剪后索引数量: {}", i+1, indices->size());
        hasFiltering = true;
      }

      // 根据最终indices创建结果点云
      if (hasFiltering && indices->size() != input->size()) {
        auto result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
        pcl::copyPointCloud(*input, *indices, *result);
        SPDLOG_DEBUG("包围盒反向ROI最终结果点云大小: {}", result->size());
        return result;
      } else {
        // 无过滤或点数未变化，返回原始点云
        SPDLOG_DEBUG("包围盒反向ROI无变化，返回原始点云");
        return input;
      }
    } else {
      // 正向ROI：合并所有包围盒的裁剪结果
      auto result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      const auto& results = computeOutputList(input, false);
      for (const auto& pcd : results) {
        *result += *pcd;  // 合并各个包围盒的裁剪结果
      }
      SPDLOG_DEBUG("包围盒正向ROI裁剪后点云大小: {}", result->size());
      return result;
    }
  }

    void PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse)
    {
      // 调用主计算函数并将结果赋值给输出参数
      output = compute(input, inverse);
    }

  std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> PcdCropBox::computeOutputList( const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse)
  {
    SPDLOG_DEBUG("输入点云大小: {}", input->size());

    // 检查输入参数有效性
    if (!input || input->empty()) {
      SPDLOG_WARN("输入点云为空或无效");
      return std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>();
    }

    if (boxes.empty()) {
      SPDLOG_WARN("包围盒配置为空");
      return std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>();
    }

    // 反向ROI警告：computeOutputList在反向模式下的语义可能不符合预期
    if (inverse) {
      SPDLOG_WARN("注意：computeOutputList在反向ROI模式下返回每个包围盒独立的反向结果，"
                  "这通常不是期望的行为。建议使用compute()函数获取整体反向ROI结果。");
    }

    // 存储每个包围盒的裁剪结果
    std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> result;
    result.reserve(boxes.size());  // 预分配空间提高性能

    // 创建包围盒裁剪器
    pcl::CropBox<pcl::PointXYZ> crop_box;
    crop_box.setInputCloud(input);
    crop_box.setNegative(inverse);  // 设置是否反向ROI

    int index = 0;
    for (const auto& box : boxes){
      // 获取包围盒的裁剪参数
      auto [min, max, translation, rotation] = get_box_filter(box);

      // 设置包围盒参数
      crop_box.setMin(min);
      crop_box.setMax(max);
      crop_box.setTranslation(translation);
      crop_box.setRotation(rotation); // rx, ry, rz顺序

      // 使用indices优化，避免不必要的点云拷贝
      pcl::IndicesPtr step_indices = std::make_shared<pcl::Indices>();
      crop_box.filter(*step_indices);

      // 根据indices创建结果点云
      auto stepResult = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      pcl::copyPointCloud(*input, *step_indices, *stepResult);

      SPDLOG_DEBUG("第{}个包围盒{}裁剪后点云大小: {}", ++index, inverse ? "反向" : "正向", stepResult->size());
      result.push_back(stepResult);
    }
    return result;
  }

  void PcdCropBox::computeOutputList(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>& output, bool inverse)
  {
      // 调用主计算函数并将结果赋值给输出参数
      output = computeOutputList(input, inverse);
  }

  void PcdCropHull::init_config(const std::vector<pcl::PointXYZ>& cfg) {
    // 保存ROI顶点配置
    this->roi_pts = cfg;
    SPDLOG_DEBUG("初始化凸包ROI配置，顶点数量: {}", cfg.size());

    // 检查顶点数量是否足够构建凸包
    if (cfg.size() < 3) {
      SPDLOG_WARN("ROI顶点数量不足3个，无法构建有效凸包");
    }
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse) {
    // 检查输入参数有效性
    if (!input || input->empty()) {
      SPDLOG_WARN("输入点云为空或无效");
      return std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    }

    if (roi_pts.empty()) {
      SPDLOG_WARN("ROI点集为空，返回原始点云");
      return input;
    }

    if (roi_pts.size() < 3) {
      SPDLOG_WARN("ROI点数量少于3个，无法构建凸包，返回原始点云");
      return input;
    }

    // 创建顶点点云并填充ROI点
    pcl::PointCloud<pcl::PointXYZ>::Ptr vertices(new pcl::PointCloud<pcl::PointXYZ>);
    vertices->points.reserve(roi_pts.size());
    for (const auto& pt : roi_pts) {
      vertices->points.push_back(pt);
    }
    vertices->width = vertices->points.size();
    vertices->height = 1;
    vertices->is_dense = true;

    SPDLOG_DEBUG("凸包顶点数量: {}", vertices->size());

    // 创建凸包对象
    pcl::ConvexHull<pcl::PointXYZ> hull;
    hull.setInputCloud(vertices);
    hull.setDimension(3);

    // 计算凸包
    std::vector<pcl::Vertices> polygons;
    pcl::PointCloud<pcl::PointXYZ>::Ptr surface_hull(new pcl::PointCloud<pcl::PointXYZ>);
    hull.reconstruct(*surface_hull, polygons);
    SPDLOG_DEBUG("凸包面片数量: {}", polygons.size());

    // 检查凸包是否构建成功
    if (polygons.empty() || surface_hull->empty()) {
      SPDLOG_WARN("凸包构建失败，返回原始点云");
      return input;
    }

    // 应用凸包裁剪滤波器
    pcl::CropHull<pcl::PointXYZ> crop;
    crop.setDim(3);
    crop.setInputCloud(input);
    crop.setHullIndices(polygons);
    crop.setHullCloud(surface_hull);
    crop.setCropOutside(inverse);  // 设置是否反向ROI

    auto result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    crop.filter(*result);
    SPDLOG_DEBUG("凸包{}裁剪后点云大小: {}", inverse ? "反向" : "正向", result->size());
    return result;
  }

  void PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse){
      // 调用主计算函数并将结果赋值给输出参数
      output = compute(input, inverse);
  }

};