#include "PcdPreprocess.hpp"
#include <spdlog/spdlog.h>
#include <pcl/common/common.h>
#include <pcl/io/ply_io.h>
#include <yaml-cpp/yaml.h>

#include <Eigen/Dense>
#include <iostream>
#include <pcl/common/transforms.h> 
#include <pcl/filters/passthrough.h>
#include "pcl/filters/crop_box.h"
#include "pcl/filters/crop_hull.h"
#include "pcl/keypoints/harris_3d.h"
#include "pcl/surface/convex_hull.h"

namespace algorithm {
  void PcdCropMinmaxXYZ::init_config(const PcdCropMinmaxXYZConfigurable& cfg)
  {
    this->cfg = cfg;
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropMinmaxXYZ::compute(pcl::PointCloud<pcl::PointXYZ>::Ptr input)
  {
    pcl::PassThrough<pcl::PointXYZ> pass;
    pass.setInputCloud(input);
    pcl::IndicesPtr indices = std::make_shared<pcl::Indices>();
    bool hasFiltering = false;

    // Define filter parameters: field name, min value, max value
    struct FilterParams {
      const char* field_name;
      float min_val;
      float max_val;
    };

    FilterParams filters[] = {
      {"x", cfg.min_x, cfg.max_x},
      {"y", cfg.min_y, cfg.max_y},
      {"z", cfg.min_z, cfg.max_z}
    };

    // Apply filters sequentially
    for (const auto& filter : filters) {
      if (filter.max_val != FLT_MAX || filter.min_val != -FLT_MAX) {
        if (hasFiltering) {
          // Use previous filtering results as input for next filter
          pass.setIndices(indices);
        }
        pass.setFilterFieldName(filter.field_name);
        pass.setFilterLimits(filter.min_val, filter.max_val);
        pass.filter(*indices);
        SPDLOG_DEBUG("indices size after filter {}: {}", filter.field_name, indices->size());
        hasFiltering = true;
      }
    }

    // Return filtered result or original input
    if (hasFiltering && indices->size() != input->size()) {
      pcl::PointCloud<pcl::PointXYZ>::Ptr result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      pcl::copyPointCloud(*input, *indices, *result);
      return result;
    } else {
      return input;
    }
  }

  void PcdCropMinmaxXYZ::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output){
      output = compute(input);
  }

  void PcdCropBox::init_config(const std::vector<algorithm::BoxInfo>& cfg)
  {
    this->boxes = cfg;
  }

  /**
   * Get box cropping parameters: min/max bounds, rotation, and translation
   * @param box User-defined box data
   * @return Tuple containing min bounds, max bounds, translation, and rotation
   */
  std::tuple<Eigen::Vector4f,Eigen::Vector4f,Eigen::Vector3f,Eigen::Vector3f> get_box_filter(const BoxInfo& box)
  {
    // min and max represent the diagonal endpoints of the untransformed (before translation+rotation) box
    // calculated from the three edge lengths
    Eigen::Vector4f min;
    Eigen::Vector4f max;
    min[0] = -box.lengthx / 2;
    min[1] = -box.widthy / 2;
    min[2] = -box.depthz / 2;
    min[3] = 1;
    max[0] = box.lengthx / 2;
    max[1] = box.widthy / 2;
    max[2] = box.depthz / 2;
    max[3] = 1;

    Eigen::Quaternion<float> rotate_quat(box.quat.w(),box.quat.x(),box.quat.y(),box.quat.z());
    // Convert rotation matrix to Euler angles (Z-Y-X order)
    Eigen::Vector3f eulerAngle = rotate_quat.matrix().eulerAngles(2, 1, 0);
    // CropBox uses (rx,ry,rz) order instead of (rz,ry,rx)
    Eigen::Vector3f rotation = Eigen::Vector3f(eulerAngle(2), eulerAngle(1), eulerAngle(0));

    return std::make_tuple(min, max, box.translation, rotation);
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input) {
    auto result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    const auto& results = computeOutputList(input);
    for (const auto& pcd : results) {
      *result += *pcd;
    }
    SPDLOG_DEBUG("filtered point size: {}", result->size());
    return result;
  }

    void PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output)
    {
      output = compute(input);
    }

  std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> PcdCropBox::computeOutputList( const pcl::PointCloud<pcl::PointXYZ>::Ptr input)
  {
    SPDLOG_DEBUG("input point size: {}", input->size());
    std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> result;
    pcl::CropBox<pcl::PointXYZ> crop_box;
    crop_box.setInputCloud(input);

    int index = 0;
    for (const auto& box : boxes){
      auto [min,max,translation, rotation] = get_box_filter(box);
      crop_box.setMin(min);
      crop_box.setMax(max);
      crop_box.setTranslation(translation);
      crop_box.setRotation(rotation); // rx, ry, rz order

      auto stepResult = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      crop_box.filter(*stepResult);
      SPDLOG_DEBUG("filtered pointcloud{} point size: {}", ++index, stepResult->size());
      result.push_back(stepResult);
    }
    return result;
  }

  void PcdCropBox::computeOutputList(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>& output)
  {
      output = computeOutputList(input);
  }

  void PcdCropHull::init_config(const std::vector<pcl::PointXYZ>& cfg) {
    this->roi_pts = cfg;
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input) {
    // Create vertices point cloud and populate it with roi_pts
    pcl::PointCloud<pcl::PointXYZ>::Ptr vertices(new pcl::PointCloud<pcl::PointXYZ>);
    vertices->points.reserve(roi_pts.size());
    for (const auto& pt : roi_pts) {
      vertices->points.push_back(pt);
    }
    vertices->width = vertices->points.size();
    vertices->height = 1;
    vertices->is_dense = true;

    SPDLOG_DEBUG("hull point num: {}", vertices->size());

    // Create convex hull object
    pcl::ConvexHull<pcl::PointXYZ> hull;
    hull.setInputCloud(vertices);
    hull.setDimension(3);

    // Compute convex hull
    std::vector<pcl::Vertices> polygons;
    pcl::PointCloud<pcl::PointXYZ>::Ptr surface_hull(new pcl::PointCloud<pcl::PointXYZ>);
    hull.reconstruct(*surface_hull, polygons);
    SPDLOG_DEBUG("convex hull vertices count: {}", polygons.size());

    // Apply crop hull filter
    pcl::CropHull<pcl::PointXYZ> crop;
    crop.setDim(3);
    crop.setInputCloud(input);
    crop.setHullIndices(polygons);
    crop.setHullCloud(surface_hull);

    auto result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    crop.filter(*result);
    SPDLOG_DEBUG("filtered pointcloud point size: {}", result->size());
    return result;
  }

  void PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output){
      output = compute(input);
  }

};