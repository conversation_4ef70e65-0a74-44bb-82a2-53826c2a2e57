#include "PcdPreprocess.hpp"
#include <spdlog/spdlog.h>
#include <pcl/common/common.h>
#include <pcl/io/ply_io.h>
#include <yaml-cpp/yaml.h>

#include <Eigen/Dense>
#include <iostream>
#include <pcl/common/transforms.h> 
#include <pcl/filters/passthrough.h>
#include "pcl/filters/crop_box.h"
#include "pcl/filters/crop_hull.h"
#include "pcl/keypoints/harris_3d.h"
#include "pcl/surface/convex_hull.h"

namespace algorithm {
  void PcdCropMinmaxXYZ::init_config(const PcdCropMinmaxXYZConfigurable& cfg)
  {
    this->cfg = cfg;
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropMinmaxXYZ::compute(pcl::PointCloud<pcl::PointXYZ>::Ptr input)
  {
    pcl::PassThrough<pcl::PointXYZ> pass;//设置滤波器对象
    pass.setInputCloud(input);//设置输入点云
    pcl::IndicesPtr indices = std::make_shared<pcl::Indices>(); //每次过滤后的点的索引
    bool anyOneStepIsExec = false;

    if (cfg.max_x != FLT_MAX || cfg.min_x != -FLT_MAX)
    {
      /*首次过滤无需setIndices*/
      pass.setFilterFieldName ("x");//设置过滤时所需要点云类型的x字段
      pass.setFilterLimits (cfg.min_x,cfg.max_x);//设置在过滤字段上的范围
      pass.filter(*indices);
      SPDLOG_DEBUG("indices size after filter x: {}", indices->size());
      anyOneStepIsExec = true;
    }
    if (cfg.max_y != FLT_MAX || cfg.min_y != -FLT_MAX)
    {
      if (anyOneStepIsExec) {
        /*将前次过滤后的indices作为本次的输入*/
        pass.setIndices(indices);
      }
      pass.setFilterFieldName ("y");//设置过滤时所需要点云类型的y字段
      pass.setFilterLimits (cfg.min_y,cfg.max_y);//设置在过滤字段上的范围
      pass.filter(*indices);
      SPDLOG_DEBUG("indices size after filter y: {}", indices->size());
      anyOneStepIsExec = true;
    }
    if (cfg.max_z != FLT_MAX || cfg.min_z != -FLT_MAX)
    {
      if (anyOneStepIsExec) {
        /*将前次过滤后的indices作为本次的输入*/
        pass.setIndices(indices);
      }
      pass.setFilterFieldName ("z");//设置过滤时所需要点云类型的z字段
      pass.setFilterLimits (cfg.min_z,cfg.max_z);//设置在过滤字段上的范围
      pass.filter(*indices);
      SPDLOG_DEBUG("indices size after filter z: {}", indices->size());
      anyOneStepIsExec = true;
    }

    if (anyOneStepIsExec && indices->size() != input->size()) {
      /*实际有过滤了则以indices拷贝输出*/
      pcl::PointCloud<pcl::PointXYZ>::Ptr result = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
      pcl::copyPointCloud(*input, *indices, *result);
      return result;
    } else {
      /*否则输出源点云*/
      return input;
    }
  };

  void PcdCropMinmaxXYZ::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output){
      auto result = compute(input);
      result.swap(output);
  }

  void PcdCropBox::init_config(const std::vector<algorithm::BoxInfo>& cfg)
  {
    this->boxs = cfg;
  }

  /*
    获取box裁剪的最大，最小，旋转，平移参数
    @param 用户编辑的box相关数据
    @param 最大，最小，旋转，平移结果
    */
  std::tuple<Eigen::Vector4f,Eigen::Vector4f,Eigen::Vector3f,Eigen::Vector3f> get_box_filter(const BoxInfo& box)
  {
    /*min、max分别代表未经转换（平移+旋转）的长方体的对角线的两个端点，可从三条边长计算得出*/
    Eigen::Vector4f min;
    Eigen::Vector4f max;
    min[0] = -box.lengthx / 2;
    min[1] = -box.widthy / 2;
    min[2] = -box.depthz / 2;
    min[3] = 1;
    max[0] = box.lengthx / 2;
    max[1] = box.widthy / 2;
    max[2] = box.depthz / 2;
    max[3] = 1;

    Eigen::Quaternion<float> rotate_quat(box.quat.w(),box.quat.x(),box.quat.y(),box.quat.z());
    // 将旋转矩阵转成欧拉角（Z-Y-X)
    Eigen::Vector3f eulerAngle = rotate_quat.matrix().eulerAngles(2, 1, 0);
    // 在cropBox使用的是（rx,ry,rz）而不是（rz,ry,rx）
    Eigen::Vector3f rotation = Eigen::Vector3f(eulerAngle(2), eulerAngle(1), eulerAngle(0));

    return std::make_tuple(min, max, box.translation, rotation);
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input) {
    pcl::PointCloud<pcl::PointXYZ>::Ptr result = pcl::PointCloud<pcl::PointXYZ>().makeShared();
    const auto& results = computeOutputList(input);
    for (auto& pcd : results) {
      *result += *pcd;
    }
    SPDLOG_DEBUG("filtered point size: {}", result->size());
    return result;
  }

    void PcdCropBox::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output)
    {
      output->clear();
      const auto& results = computeOutputList(input);
      for (auto& pcd : results) {
        *output += *pcd;
      }
      SPDLOG_DEBUG("filtered point size: {}", output->size());
    }

  std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> PcdCropBox::computeOutputList( const pcl::PointCloud<pcl::PointXYZ>::Ptr input)
  {
    SPDLOG_DEBUG("input point size: {}", input->size());
    std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> result;
    pcl::CropBox<pcl::PointXYZ> crop_box;
    crop_box.setInputCloud(input);

    int index = 0;
    for (auto& box : boxs){
      auto [min,max,translation, rotation] = get_box_filter(box);
      crop_box.setMin(min);
      crop_box.setMax(max);
      crop_box.setTranslation(translation);
      crop_box.setRotation(rotation); //rx,ry,rz

      pcl::PointCloud<pcl::PointXYZ>::Ptr stepResult = pcl::PointCloud<pcl::PointXYZ>().makeShared();
      crop_box.filter(*stepResult);
      SPDLOG_DEBUG("filtered pointcloud{} point size: {}", ++index, stepResult->size());
      result.push_back(stepResult);
    }
    return result;
  }

  void PcdCropBox::computeOutputList(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>& output)
  {
      auto reuslt = computeOutputList(input);
      reuslt.swap(output);
  }

  void PcdCropHull::init_config(const std::vector<pcl::PointXYZ>& cfg) {
    this->roi_pts = cfg;
  }

  pcl::PointCloud<pcl::PointXYZ>::Ptr PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input) {
    pcl::PointCloud<pcl::PointXYZ>::Ptr vertices(new pcl::PointCloud<pcl::PointXYZ>);
    SPDLOG_DEBUG("hull point num=", roi_pts.size());
    pcl::ConvexHull<pcl::PointXYZ> hull;//创建凸包对象
    hull.setInputCloud(vertices);//设置输入点云
    hull.setDimension(3);//设置凸包维度
    std::vector<pcl::Vertices> polygons;//设置pcl::Vertices类型的量，用于保存凸包顶点
    pcl::PointCloud<pcl::PointXYZ>::Ptr surface_hull (new pcl::PointCloud<pcl::PointXYZ>);//该点云用于描述凸包形状
    hull.reconstruct(*surface_hull, polygons);//计算2D凸包结果
    SPDLOG_DEBUG("凸包顶点数:{}",polygons.size());
    pcl::CropHull<pcl::PointXYZ> crop;
    crop.setDim(3);//设置维度
    crop.setInputCloud(input);//设置需要滤波的点云
    crop.setHullIndices(polygons);//输入封闭多边形的顶点
    crop.setHullCloud(surface_hull);//输入封闭多边形的形状
    pcl::PointCloud<pcl::PointXYZ>::Ptr result = pcl::PointCloud<pcl::PointXYZ>().makeShared();
    crop.filter(*result);//执行CropHull滤波，存储结果到result
    SPDLOG_DEBUG("filtered pointcloud point size: {}", result->size());
    return result;
  }

  void PcdCropHull::compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output){
      auto result = compute(input);
      result.swap(output);
  }

};