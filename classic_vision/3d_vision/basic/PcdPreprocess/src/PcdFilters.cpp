
#include "PcdFilters.hpp"

#include <pcl/common/common.h>
#include <pcl/filters/approximate_voxel_grid.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/filters/radius_outlier_removal.h>
#include <pcl/filters/statistical_outlier_removal.h>
#include <pcl/filters/uniform_sampling.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/search/kdtree.h>
#include <pcl/segmentation/extract_clusters.h>
#include <pcl/surface/mls.h>

namespace algorithm {
namespace cloudfilter {
pcl::PointCloud<pcl::PointXYZ>::Ptr voxelDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float voxel_size) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();
  pcl::VoxelGrid<pcl::PointXYZ> filter;
  filter.setInputCloud(cloud_ptr);
  filter.setLeafSize(voxel_size, voxel_size, voxel_size);
  filter.filter(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr uniformDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float radius) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();
  pcl::UniformSampling<pcl::PointXYZ> filter;
  filter.setInputCloud(cloud_ptr);
  filter.setRadiusSearch(radius);
  filter.filter(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr approximateVoxelDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float voxel_size) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();
  pcl::ApproximateVoxelGrid<pcl::PointXYZ> filter;
  filter.setInputCloud(cloud_ptr);
  filter.setLeafSize(voxel_size, voxel_size, voxel_size);
  filter.filter(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersStatistical(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, int mean_k, double std_dev_mul_thresh) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();
  pcl::StatisticalOutlierRemoval<pcl::PointXYZ> filter;
  filter.setInputCloud(cloud_ptr);
  filter.setMeanK(mean_k);
  filter.setStddevMulThresh(std_dev_mul_thresh);
  filter.filter(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersRadiusSearch(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float radius, int min_neighbors) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();
  pcl::RadiusOutlierRemoval<pcl::PointXYZ> filter;
  filter.setInputCloud(cloud_ptr);
  filter.setMinNeighborsInRadius(min_neighbors);
  filter.setRadiusSearch(radius);
  filter.filter(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersEuclideanCluster(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, double cluster_tolerance, int min_cluster_size, int max_cluster_size) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();

  pcl::search::KdTree<pcl::PointXYZ>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZ>);
  tree->setInputCloud(cloud_ptr);

  std::vector<pcl::PointIndices> cluster_indices;
  pcl::EuclideanClusterExtraction<pcl::PointXYZ> ec;
  ec.setClusterTolerance(cluster_tolerance);
  ec.setMinClusterSize(min_cluster_size);
  ec.setMaxClusterSize(max_cluster_size);
  ec.setSearchMethod(tree);
  ec.setInputCloud(cloud_ptr);
  ec.extract(cluster_indices);

  for (const auto& indices : cluster_indices) {
    for (const auto& idx : indices.indices) {
      filtered_cloud_ptr->points.push_back(cloud_ptr->points[idx]);
    }
  }
  filtered_cloud_ptr->width = filtered_cloud_ptr->points.size();
  filtered_cloud_ptr->height = 1;
  filtered_cloud_ptr->is_dense = true;
  return filtered_cloud_ptr;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr smoothMLS(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, double search_radius, double upsampling_radius, double upsampling_step) {
  auto filtered_cloud_ptr = pcl::PointCloud<pcl::PointXYZ>().makeShared();

  pcl::search::KdTree<pcl::PointXYZ>::Ptr tree(new pcl::search::KdTree<pcl::PointXYZ>);
  tree->setInputCloud(cloud_ptr);

  pcl::MovingLeastSquares<pcl::PointXYZ, pcl::PointXYZ> mls;
  mls.setInputCloud(cloud_ptr);
  mls.setSearchMethod(tree);
  mls.setSearchRadius(search_radius);

  // 设置多项式拟合阶数，用于平滑
  mls.setPolynomialOrder(2);

  // 如果需要上采样，设置上采样参数
  if (upsampling_radius > 0 && upsampling_step > 0) {
    mls.setUpsamplingMethod(pcl::MovingLeastSquares<pcl::PointXYZ, pcl::PointXYZ>::SAMPLE_LOCAL_PLANE);
    mls.setUpsamplingRadius(upsampling_radius);
    mls.setUpsamplingStepSize(upsampling_step);
  } else {
    // 仅做平滑，不上采样
    mls.setUpsamplingMethod(pcl::MovingLeastSquares<pcl::PointXYZ, pcl::PointXYZ>::NONE);
  }

  mls.process(*filtered_cloud_ptr);
  return filtered_cloud_ptr;
}
};  // namespace cloudfilter
};  // namespace algorithm