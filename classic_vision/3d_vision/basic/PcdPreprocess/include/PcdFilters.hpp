
#pragma once

#include <climits>
#include <pcl/common/common.h>

namespace algorithm {
namespace cloudfilter {
/** @brief 体素滤波[下采样]
 *  @param cloud_ptr 输入点云
 *  @param voxel_size 体素大小
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr voxelDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float voxel_size);

/** @brief 均匀采样[下采样]
 *  @param cloud_ptr 输入点云
 *  @param radius 搜索半径
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr uniformDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float radius);

/** @brief 近似体素滤波[下采样]
 *  @param cloud_ptr 输入点云
 *  @param voxel_size 体素大小
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr approximateVoxelDownSample(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float voxel_size);

/** @brief 统计滤波[去除离群点]
 *  @param cloud_ptr 输入点云
 *  @param mean_k 每个点参考的邻居数
 *  @param std_dev_mul_thresh 标准差倍数阈值
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersStatistical(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, int mean_k, double std_dev_mul_thresh = 1.0);

/** @brief 半径滤波[去除离群点]
 *  @param cloud_ptr 输入点云
 *  @param radius 搜索半径
 *  @param min_neighbors 最小邻居数
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersRadiusSearch(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, float radius, int min_neighbors = 1);

/** @brief 欧式聚类[去除离群点]
 *  @param cloud_ptr 输入点云
 *  @param cluster_tolerance 聚类容差（距离阈值）
 *  @param min_cluster_size 最小簇大小
 *  @param max_cluster_size 最大簇大小
 *  @return 过滤后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr removeOutliersEuclideanCluster(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, double cluster_tolerance, int min_cluster_size, int max_cluster_size = INT_MAX);

/** @brief 最小二乘法[平滑]
 *  @param cloud_ptr 输入点云
 *  @param search_radius 搜索半径
 *  @param upsampling_radius 上采样半径
 *  @param upsampling_step 上采样步长
 *  @return 平滑后的点云
 */
pcl::PointCloud<pcl::PointXYZ>::Ptr smoothMLS(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_ptr, double search_radius, double upsampling_radius, double upsampling_step);

};  // namespace cloudfilter
};  // namespace algorithm