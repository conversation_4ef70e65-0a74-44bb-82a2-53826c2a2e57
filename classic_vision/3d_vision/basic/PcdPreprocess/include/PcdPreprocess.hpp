#pragma once

#include <cfloat>
#include <pcl/common/common.h>
#include "PcdFilters.hpp"

namespace algorithm
{
    /** \brief [PcdCropMinmaxXYZ]的配置 */
  struct PcdCropMinmaxXYZConfigurable {
      /**X座标下限*/
    float min_x = -FLT_MAX;
      /**Y座标下限*/
    float min_y = -FLT_MAX;
      /**Z座标下限*/
    float min_z = -FLT_MAX;
      /**X座标上限*/
    float max_x = FLT_MAX;
      /**Y座标上限*/
    float max_y = FLT_MAX;
      /**Z座标上限*/
    float max_z = FLT_MAX;
  };

  /**
   * \brief 以XYZ范围裁剪点云
   * \ingroup vision_3D
   * */
  class PcdCropMinmaxXYZ {
   public:
      /** \brief 初始化配置 */
    void init_config(const PcdCropMinmaxXYZConfigurable& cfg);

    /** \brief 主函数1
     * \param[in] input 源点云
     * \param[in] inverse 是否反向ROI（默认false）
     * \return 裁剪后点云
     * */
    pcl::PointCloud<pcl::PointXYZ>::Ptr compute(pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse = false);

    /** \brief 主函数2
     * \param[in] input 源点云
     * \param[out] output 裁剪后点云
     * \param[in] inverse 是否反向ROI（默认false）
     * */
    void compute(pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse = false);
   private:
    PcdCropMinmaxXYZConfigurable cfg;
  };

  /**\brief 包围盒 */
  /*有注释的是目前用到的字段，其他没注释的字段属冗余信息*/
  struct BoxInfo {
      Eigen::Vector3f eularangle;
      float lengthx;
      float widthy;
      float depthz;
      /**包围盒中心点位置*/
      Eigen::Vector3f translation;
      /**包围盒中心点姿态*/
      Eigen::Quaternion<float> quat;
      /**包围盒的8个顶点的位置*/
      std::vector<Eigen::Vector3f> vertices;

  };

    /**
     * \brief 以包围盒裁剪点云
     * \ingroup vision_3D
     * */
  class PcdCropBox {
   public:
      /** \brief 初始化配置 */
    void init_config(const std::vector<algorithm::BoxInfo>& cfg);

      /** \brief 主函数1
       * 配置有多个包围盒时，输出单个点云
       * \param[in] input 源点云
       * \param[in] inverse 是否反向ROI（默认false）
       * \return 裁剪后点云
       * */
    pcl::PointCloud<pcl::PointXYZ>::Ptr compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse = false);

      /** \brief 主函数2
       * 配置有多个包围盒时，输出单个点云
       * \param[in] input 源点云
       * \param[out] output 裁剪后点云
       * \param[in] inverse 是否反向ROI（默认false）
       * */
    void compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse = false);

      /** \brief 主函数3
       * 配置有多个包围盒时，输出多个点云
       * \param[in] input 源点云
       * \param[in] inverse 是否反向ROI（默认false）
       * \return 裁剪后点云列表
       * */
    std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr> computeOutputList(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse = false);

      /** \brief 主函数4
       * 配置有多个包围盒时，输出多个点云
       * \param[in] input 源点云
       * \param[out] output 裁剪后点云列表
       * \param[in] inverse 是否反向ROI（默认false）
       * */
    void computeOutputList(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, std::vector<pcl::PointCloud<pcl::PointXYZ>::Ptr>& output, bool inverse = false);
   private:
    std::vector<algorithm::BoxInfo> boxes;
  };

    /**
     * \brief 以凸包裁剪点云
     * \ingroup vision_3D
     * */
  class PcdCropHull {
   public:
      /** \brief 初始化配置
       * \param cfg 凸包的顶点
       * */
    void init_config(const std::vector<pcl::PointXYZ>& cfg);

      /** \brief 主函数1
       * \param[in] input 源点云
       * \param[in] inverse 是否反向ROI（默认false）
       * \return 裁剪后点云
       * */
    pcl::PointCloud<pcl::PointXYZ>::Ptr compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, bool inverse = false);

      /** \brief 主函数2
       * \param[in] input 源点云
       * \param[out] output 裁剪后点云
       * \param[in] inverse 是否反向ROI（默认false）
       * */
    void compute(const pcl::PointCloud<pcl::PointXYZ>::Ptr input, pcl::PointCloud<pcl::PointXYZ>::Ptr& output, bool inverse = false);
   private:
    std::vector<pcl::PointXYZ> roi_pts;
  };
};
